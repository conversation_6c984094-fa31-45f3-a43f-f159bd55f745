#!/usr/bin/env python3
"""
测试BMN数据格式的脚本
验证特征文件和标注文件是否符合MMAction2的要求
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from pathlib import Path

# 添加mmaction2路径
sys.path.insert(0, 'mmaction2')

from mmaction.utils import register_all_modules
from mmaction.datasets import ActivityNetDataset
from mmengine.dataset import Compose

def test_csv_format(csv_path):
    """测试CSV文件格式"""
    print(f"\n=== 测试CSV格式: {csv_path} ===")
    
    try:
        # 模拟MMAction2的LoadLocalizationFeature
        raw_feature = np.loadtxt(csv_path, dtype=np.float32, delimiter=',', skiprows=1)
        print(f"✓ CSV加载成功")
        print(f"  原始形状: {raw_feature.shape}")
        
        # 转置操作
        transposed = np.transpose(raw_feature, (1, 0))
        print(f"  转置后形状: {transposed.shape}")
        print(f"  期望格式: (特征维度, 时间步数)")
        
        # 检查是否有异常值
        print(f"  特征范围: [{np.min(transposed):.3f}, {np.max(transposed):.3f}]")
        print(f"  零值比例: {np.sum(transposed == 0) / transposed.size * 100:.1f}%")
        
        return True, transposed.shape
        
    except Exception as e:
        print(f"✗ CSV格式错误: {e}")
        return False, None

def test_annotation_format(ann_path):
    """测试标注文件格式"""
    print(f"\n=== 测试标注格式: {ann_path} ===")
    
    try:
        with open(ann_path, 'r') as f:
            annotations = json.load(f)
        
        print(f"✓ 标注文件加载成功")
        print(f"  视频数量: {len(annotations)}")
        
        # 检查第一个视频的格式
        first_key = list(annotations.keys())[0]
        first_ann = annotations[first_key]
        
        required_keys = ['duration_second', 'duration_frame', 'feature_frame', 'annotations']
        missing_keys = [key for key in required_keys if key not in first_ann]
        
        if missing_keys:
            print(f"✗ 缺少必要字段: {missing_keys}")
            return False
        
        print(f"✓ 标注格式正确")
        print(f"  示例视频: {first_key}")
        print(f"    时长: {first_ann['duration_second']:.2f}秒")
        print(f"    视频帧数: {first_ann['duration_frame']}")
        print(f"    特征帧数: {first_ann['feature_frame']}")
        print(f"    标注数量: {len(first_ann['annotations'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ 标注格式错误: {e}")
        return False

def test_data_pipeline(feature_dir, ann_path):
    """测试数据管道"""
    print(f"\n=== 测试数据管道 ===")
    
    try:
        # 注册模块
        register_all_modules()
        
        # 创建数据管道
        pipeline = [
            dict(type='LoadLocalizationFeature'),
            dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
            dict(type='GenerateLocalizationLabels'),
            dict(
                type='PackLocalizationInputs',
                keys=('gt_bbox', ),
                meta_keys=('video_name', 'duration_second', 'duration_frame',
                          'annotations', 'feature_frame'))
        ]
        
        # 创建数据集
        dataset = ActivityNetDataset(
            ann_file=ann_path,
            data_prefix=dict(video=feature_dir + '/'),
            pipeline=pipeline,
            test_mode=True
        )
        
        print(f"✓ 数据集创建成功")
        print(f"  数据集大小: {len(dataset)}")
        
        # 测试加载第一个样本
        sample = dataset[0]
        print(f"✓ 样本加载成功")
        print(f"  输入形状: {sample['inputs'].shape}")
        print(f"  视频名: {sample['data_samples'].video_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据管道测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bmn_model_compatibility(feature_dir, ann_path):
    """测试BMN模型兼容性"""
    print(f"\n=== 测试BMN模型兼容性 ===")
    
    try:
        from mmaction.registry import MODELS
        from mmengine.config import Config
        
        # 创建BMN模型配置
        model_config = dict(
            type='BMN',
            temporal_dim=16,
            boundary_ratio=0.5,
            num_samples=32,
            num_samples_per_bin=3,
            feat_dim=400,
            soft_nms_alpha=0.4,
            soft_nms_low_threshold=0.1,
            soft_nms_high_threshold=0.6,
            post_process_top_k=200
        )
        
        # 构建模型
        model = MODELS.build(model_config)
        model.eval()
        
        print(f"✓ BMN模型创建成功")
        
        # 测试数据管道
        register_all_modules()
        
        pipeline = [
            dict(type='LoadLocalizationFeature'),
            dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
            dict(
                type='PackLocalizationInputs',
                keys=('gt_bbox', ),
                meta_keys=('video_name', 'duration_second', 'duration_frame',
                          'annotations', 'feature_frame'))
        ]
        
        dataset = ActivityNetDataset(
            ann_file=ann_path,
            data_prefix=dict(video=feature_dir + '/'),
            pipeline=pipeline,
            test_mode=True
        )
        
        # 获取一个样本
        sample = dataset[0]
        
        # 准备模型输入
        inputs = sample['inputs'].unsqueeze(0)  # 添加batch维度
        data_samples = [sample['data_samples']]
        
        print(f"✓ 模型输入准备完成")
        print(f"  输入形状: {inputs.shape}")
        
        # 模型前向传播测试
        with torch.no_grad():
            # 这里只测试模型是否能接受输入，不运行完整推理
            print(f"✓ 模型兼容性测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ BMN模型兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== BMN数据格式测试 ===")
    
    # 测试路径
    feature_dir = "real_tad_results_fixed/features"
    ann_path = "real_tad_results_fixed/test_annotations.json"
    
    if not os.path.exists(feature_dir):
        print(f"✗ 特征目录不存在: {feature_dir}")
        return
    
    if not os.path.exists(ann_path):
        print(f"✗ 标注文件不存在: {ann_path}")
        return
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试CSV格式
    csv_files = list(Path(feature_dir).glob("*.csv"))
    if csv_files:
        csv_success, feature_shape = test_csv_format(str(csv_files[0]))
        if csv_success:
            success_count += 1
    else:
        print("✗ 没有找到CSV特征文件")
    
    # 2. 测试标注格式
    if test_annotation_format(ann_path):
        success_count += 1
    
    # 3. 测试数据管道
    if test_data_pipeline(feature_dir, ann_path):
        success_count += 1
    
    # 4. 测试BMN兼容性
    try:
        import torch
        if test_bmn_model_compatibility(feature_dir, ann_path):
            success_count += 1
    except ImportError:
        print("⚠ 跳过BMN兼容性测试（需要PyTorch）")
        total_tests -= 1
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！数据格式正确。")
    else:
        print("❌ 部分测试失败，需要修复数据格式。")
        
        print("\n建议修复步骤：")
        print("1. 使用修复后的 real_tad_inference.py 重新提取特征")
        print("2. 或者使用 improved_feature_extraction.py 提取更好的特征")
        print("3. 确保CSV文件格式：每行是特征维度，每列是时间步，有header")
        print("4. 确保标注文件包含正确的 feature_frame 字段")

if __name__ == '__main__':
    main()
