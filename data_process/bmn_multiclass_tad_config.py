# Copyright (c) OpenMMLab. All rights reserved.
# BMN配置文件 - 用于多类别时间动作定位
# 基于SlowOnly R50特征 (2048维，100时间步)

_base_ = [
    '../mmaction2/configs/_base_/models/bmn_400x100.py',
    '../mmaction2/configs/_base_/default_runtime.py'
]

# 模型设置 - 修改特征维度为2048
model = dict(
    type='BMN',
    temporal_dim=100,           # 时间维度：100个时间步
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=2048,              # SlowOnly R50特征维度
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.5,
    soft_nms_high_threshold=0.9,
    post_process_top_k=100)

# 数据集设置
dataset_type = 'ActivityNetDataset'
data_root = '../data/MultiClassTAD/features_slowonly'
data_root_val = '../data/MultiClassTAD/features_slowonly'
ann_file_train = '../data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = '../data/MultiClassTAD/multiclass_tad_val.json'
ann_file_test = '../data/MultiClassTAD/multiclass_tad_val.json'

# 训练数据管道
train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

# 验证数据管道
val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 测试数据管道
test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

# 数据加载器
train_dataloader = dict(
    batch_size=4,               # 根据GPU内存调整
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    drop_last=True,
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 训练配置
max_epochs = 20
train_cfg = dict(
    type='EpochBasedTrainLoop',
    max_epochs=max_epochs,
    val_begin=1,
    val_interval=2)

val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 优化器配置
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.001, weight_decay=0.0001),
    clip_grad=dict(max_norm=40, norm_type=2))

# 学习率调度
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_epochs,
        by_epoch=True,
        milestones=[15],        # 在第15个epoch降低学习率
        gamma=0.1)
]

# 工作目录
work_dir = '../work_dirs/bmn_multiclass_tad_slowonly'

# 评估器
test_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(out=f'{work_dir}/results.json', output_format='json'))
val_evaluator = test_evaluator

# 默认钩子
default_hooks = dict(
    runtime_info=dict(type='RuntimeInfoHook'),
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=10, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=5, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 环境配置
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

# 可视化配置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(type='ActionVisualizer', vis_backends=vis_backends)

# 日志配置
log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'

# 加载配置
load_from = None
resume = False
